/**
 * الفئة الرئيسية لروبوت التداول
 */

const { QuotexConnection } = require('../connection/QuotexConnection');
const { DataManager } = require('../data/DataManager');
const { TradeManager } = require('../trading/TradeManager');
const { StrategyManager } = require('../strategy/StrategyManager');

class TradingBot {
    constructor(config, logger) {
        this.config = config;
        this.logger = logger;
        this.isRunning = false;
        this.isShuttingDown = false;
        
        // تهيئة المكونات الأساسية
        this.connection = new QuotexConnection(config, logger);
        this.dataManager = new DataManager(config, logger);
        this.tradeManager = new TradeManager(config, logger);
        this.strategyManager = new StrategyManager(config, logger);
        
        // ربط الأحداث
        this.setupEventHandlers();
    }

    /**
     * إعداد معالجات الأحداث
     */
    setupEventHandlers() {
        // أحداث الاتصال
        this.connection.on('connected', () => {
            this.logger.connection('تم الاتصال بمنصة Quotex بنجاح');
        });

        this.connection.on('disconnected', () => {
            this.logger.connection('تم قطع الاتصال مع منصة Quotex');
        });

        this.connection.on('error', (error) => {
            this.logger.error('خطأ في الاتصال:', error);
        });

        // أحداث البيانات
        this.dataManager.on('candleUpdate', (data) => {
            this.logger.data('تحديث شمعة جديدة', { symbol: data.symbol });
            this.strategyManager.processCandleUpdate(data);
        });

        this.dataManager.on('balanceUpdate', (balance) => {
            this.logger.info(`💰 تحديث الرصيد: $${balance}`);
        });

        // أحداث التداول
        this.tradeManager.on('tradeOpened', (trade) => {
            this.logger.trade('تم فتح صفقة جديدة', trade);
        });

        this.tradeManager.on('tradeClosed', (trade) => {
            this.logger.trade('تم إغلاق صفقة', trade);
        });

        this.tradeManager.on('tradeError', (error) => {
            this.logger.error('خطأ في التداول:', error);
        });

        // أحداث الاستراتيجية
        this.strategyManager.on('signal', (signal) => {
            this.logger.strategy('إشارة تداول جديدة', signal);
            this.handleTradingSignal(signal);
        });
    }

    /**
     * بدء تشغيل الروبوت
     */
    async start() {
        try {
            this.logger.info('🚀 بدء تشغيل روبوت التداول...');
            
            // التحقق من الإعدادات
            await this.validateConfig();
            
            // الاتصال بالمنصة
            await this.connection.connect();
            
            // تهيئة مدير البيانات
            await this.dataManager.initialize(this.connection);
            
            // تهيئة مدير التداول
            await this.tradeManager.initialize(this.connection);
            
            // تهيئة مدير الاستراتيجية
            await this.strategyManager.initialize();
            
            // بدء جمع البيانات
            await this.dataManager.startDataCollection();

            // انتظار قليل ثم تحليل البيانات المحفوظة
            setTimeout(async () => {
                try {
                    const analysis = await this.connection.analyzeMonitoredData();
                    if (analysis) {
                        this.logger.info('📈 تحليل البيانات المحفوظة:', analysis);
                    }
                } catch (error) {
                    this.logger.debug('خطأ في تحليل البيانات:', error);
                }
            }, 30000); // بعد 30 ثانية

            this.isRunning = true;
            this.logger.info('✅ تم تشغيل الروبوت بنجاح');
            
            // بدء حلقة المراقبة الرئيسية
            this.startMainLoop();
            
        } catch (error) {
            this.logger.error('❌ فشل في تشغيل الروبوت:', error);
            throw error;
        }
    }

    /**
     * إيقاف الروبوت
     */
    async shutdown() {
        if (this.isShuttingDown) return;
        
        this.isShuttingDown = true;
        this.logger.info('📴 بدء إيقاف الروبوت...');
        
        try {
            // إيقاف الاستراتيجية
            if (this.strategyManager) {
                await this.strategyManager.stop();
            }
            
            // إغلاق الصفقات المفتوحة
            if (this.tradeManager) {
                await this.tradeManager.closeAllTrades();
            }
            
            // إيقاف جمع البيانات
            if (this.dataManager) {
                await this.dataManager.stop();
            }
            
            // قطع الاتصال
            if (this.connection) {
                await this.connection.disconnect();
            }
            
            this.isRunning = false;
            this.logger.info('✅ تم إيقاف الروبوت بنجاح');
            
        } catch (error) {
            this.logger.error('❌ خطأ أثناء إيقاف الروبوت:', error);
        }
    }

    /**
     * التحقق من صحة الإعدادات
     */
    async validateConfig() {
        const requiredSettings = [
            'connection.url',
            'trading.defaultAmount',
            'data.timeframe'
        ];
        
        for (const setting of requiredSettings) {
            if (!this.config.get(setting)) {
                throw new Error(`إعداد مطلوب مفقود: ${setting}`);
            }
        }
        
        this.logger.info('✅ تم التحقق من الإعدادات بنجاح');
    }

    /**
     * حلقة المراقبة الرئيسية
     */
    startMainLoop() {
        const interval = setInterval(async () => {
            if (!this.isRunning || this.isShuttingDown) {
                clearInterval(interval);
                return;
            }
            
            try {
                // مراقبة حالة الاتصال
                await this.monitorConnection();
                
                // مراقبة الصفقات
                await this.monitorTrades();
                
                // مراقبة الرصيد
                await this.monitorBalance();
                
            } catch (error) {
                this.logger.error('خطأ في حلقة المراقبة:', error);
            }
        }, 5000); // كل 5 ثوان
    }

    /**
     * مراقبة حالة الاتصال
     */
    async monitorConnection() {
        if (!this.connection.isConnected()) {
            this.logger.warn('⚠️ فقدان الاتصال، محاولة إعادة الاتصال...');
            await this.connection.reconnect();
        }
    }

    /**
     * مراقبة الصفقات
     */
    async monitorTrades() {
        await this.tradeManager.updateTrades();
    }

    /**
     * مراقبة الرصيد
     */
    async monitorBalance() {
        const balance = await this.dataManager.getBalance();
        if (balance !== null) {
            // التحقق من حدود المخاطرة
            const stopLoss = this.config.get('trading.stopLoss');
            if (stopLoss && balance < stopLoss) {
                this.logger.warn('⚠️ تم الوصول لحد الخسارة، إيقاف التداول');
                await this.strategyManager.pause();
            }
        }
    }

    /**
     * معالجة إشارة التداول
     */
    async handleTradingSignal(signal) {
        try {
            if (!this.isRunning || this.isShuttingDown) return;
            
            // التحقق من شروط التداول
            const canTrade = await this.tradeManager.canOpenTrade(signal);
            if (!canTrade) {
                this.logger.warn('⚠️ لا يمكن فتح صفقة في الوقت الحالي');
                return;
            }
            
            // فتح الصفقة
            await this.tradeManager.openTrade(signal);
            
        } catch (error) {
            this.logger.error('خطأ في معالجة إشارة التداول:', error);
        }
    }

    /**
     * الحصول على حالة الروبوت
     */
    getStatus() {
        return {
            isRunning: this.isRunning,
            isShuttingDown: this.isShuttingDown,
            connection: this.connection.getStatus(),
            trades: this.tradeManager.getStatus(),
            balance: this.dataManager.getCurrentBalance(),
            strategy: this.strategyManager.getStatus()
        };
    }
}

module.exports = { TradingBot };
