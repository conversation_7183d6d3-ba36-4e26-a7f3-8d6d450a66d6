const puppeteer = require('puppeteer-extra');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');
const fs = require('fs').promises;
const fsSync = require('fs');
const path = require('path');
const http = require('http');

// Use stealth plugin to avoid detection
puppeteer.use(StealthPlugin());

// Create output directories
const outputDir = path.join(__dirname, 'monitoring_results');
const sessionsDir = path.join(outputDir, 'sessions');
const cookiesDir = path.join(outputDir, 'cookies');
const messagesDir = path.join(outputDir, 'messages');

// Ensure directories exist
[outputDir, sessionsDir, cookiesDir, messagesDir].forEach(dir => {
  if (!fsSync.existsSync(dir)) {
    fsSync.mkdirSync(dir, { recursive: true });
  }
});

// Global monitoring state
let monitoringState = {
  isConnected: false,
  totalMessages: 0,
  processedMessages: 0,
  sessionToken: null,
  startTime: null,
  lastActivity: null,
  messageTypes: {},
  errors: []
};

// Enhanced monitoring system
async function startQuotexMonitoring() {
  console.log('🚀 بدء نظام مراقبة Quotex المحسن...');

  // Start web dashboard
  startWebDashboard();

  const browser = await puppeteer.launch({
    headless: false,
    defaultViewport: null,
    args: [
      '--start-maximized',
      '--disable-web-security',
      '--disable-features=VizDisplayCompositor'
    ]
  });

  try {
    const page = await browser.newPage();

    // Initialize monitoring
    monitoringState.startTime = new Date();
    monitoringState.isConnected = true;

    // Setup comprehensive monitoring
    await setupAdvancedMonitoring(page);

    // Navigate to platform
    console.log('🔗 الانتقال إلى منصة Quotex...');
    await page.goto('https://qxbroker.com/en/demo-trade', {
      waitUntil: 'networkidle2',
      timeout: 60000
    });

    console.log('⏳ انتظار تحميل المنصة...');
    await page.waitForTimeout(5000);

    // Extract and save session data
    await extractAndSaveSessionData(page);

    // Start message monitoring
    await startMessageMonitoring(page);

    console.log('✅ تم بدء المراقبة بنجاح');
    console.log('📊 لوحة التحكم متاحة على: http://localhost:3000');
    console.log('⚠️ اتركني أراقب لمدة 5 دقائق على الأقل لجمع البيانات الكافية');

    // Keep monitoring for extended period
    await monitorForDuration(page, 10 * 60 * 1000); // 10 minutes
    
// Setup advanced monitoring with WebSocket interception
async function setupAdvancedMonitoring(page) {
  console.log('🔧 إعداد المراقبة المتقدمة...');

  // Inject monitoring script into page
  await page.evaluateOnNewDocument(() => {
    // Global monitoring object
    window.quotexMonitor = {
      messages: [],
      sessionData: {},
      cookies: {},
      startTime: Date.now(),
      messageCount: 0
    };

    // Override WebSocket to capture all messages
    const originalWebSocket = window.WebSocket;
    window.WebSocket = class extends originalWebSocket {
      constructor(url, protocols) {
        super(url, protocols);
        console.log('🔗 WebSocket اتصال جديد:', url);

        this.addEventListener('open', (event) => {
          console.log('✅ WebSocket متصل');
          window.quotexMonitor.isConnected = true;
        });

        this.addEventListener('message', (event) => {
          const timestamp = new Date().toISOString();
          const messageData = {
            timestamp,
            type: 'received',
            data: event.data,
            size: event.data.length
          };

          // Process message based on patterns from ws_messages.json
          if (typeof event.data === 'string') {
            // Check for important message types
            if (event.data.startsWith('0{')) {
              messageData.messageType = 'connection_init';
              // Extract session ID
              try {
                const parsed = JSON.parse(event.data.substring(1));
                if (parsed.sid) {
                  window.quotexMonitor.sessionData.socketSid = parsed.sid;
                  console.log('📋 Session ID:', parsed.sid);
                }
              } catch (e) {}
            } else if (event.data === '40') {
              messageData.messageType = 'connection_ack';
              console.log('✅ اتصال مؤكد');
            } else if (event.data.startsWith('42[')) {
              messageData.messageType = 'socket_io_message';
              console.log('📨 رسالة Socket.IO:', event.data.substring(0, 100));
            } else if (event.data.startsWith('451-[')) {
              messageData.messageType = 'instruments_list';
              console.log('📋 قائمة الأدوات المالية');
            } else if (event.data.startsWith('BF')) {
              messageData.messageType = 'base64_data';
              messageData.isEncrypted = true;
              console.log('🔐 بيانات مشفرة Base64:', event.data.substring(0, 50));

              // Try to decode Base64 data
              try {
                const decoded = atob(event.data);
                messageData.decoded = decoded.substring(0, 500); // First 500 chars
                console.log('🔓 بيانات مفكوكة:', decoded.substring(0, 100));
              } catch (e) {
                console.log('❌ فشل في فك التشفير:', e.message);
              }
            }
          }

          window.quotexMonitor.messages.push(messageData);
          window.quotexMonitor.messageCount++;

          // Keep only last 1000 messages to prevent memory issues
          if (window.quotexMonitor.messages.length > 1000) {
            window.quotexMonitor.messages = window.quotexMonitor.messages.slice(-1000);
          }
        });

        this.addEventListener('close', (event) => {
          console.log('❌ WebSocket مقطوع');
          window.quotexMonitor.isConnected = false;
        });

        this.addEventListener('error', (event) => {
          console.log('⚠️ خطأ WebSocket:', event);
        });

        // Override send to capture outgoing messages
        const originalSend = this.send;
        this.send = function(data) {
          const timestamp = new Date().toISOString();
          window.quotexMonitor.messages.push({
            timestamp,
            type: 'sent',
            data: data,
            size: data.length
          });
          console.log('📤 إرسال:', data);
          return originalSend.call(this, data);
        };
      }
    };

    console.log('✅ تم إعداد مراقب WebSocket');
  });
}

// Extract and save session data
async function extractAndSaveSessionData(page) {
  console.log('🔍 استخراج بيانات الجلسة...');

  const sessionData = await page.evaluate(() => {
    const data = {
      timestamp: new Date().toISOString(),
      url: window.location.href,
      localStorage: {},
      sessionStorage: {},
      cookies: document.cookie,
      sessionToken: null,
      userAgent: navigator.userAgent
    };

    // Extract localStorage
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      data.localStorage[key] = localStorage.getItem(key);
    }

    // Extract sessionStorage
    for (let i = 0; i < sessionStorage.length; i++) {
      const key = sessionStorage.key(i);
      data.sessionStorage[key] = sessionStorage.getItem(key);
    }

    // Search for session token in various places
    const allData = { ...data.localStorage, ...data.sessionStorage };
    for (const [key, value] of Object.entries(allData)) {
      if (key.toLowerCase().includes('session') ||
          key.toLowerCase().includes('token') ||
          key.toLowerCase().includes('auth')) {
        try {
          const parsed = JSON.parse(value);
          if (parsed.session || parsed.token) {
            data.sessionToken = parsed.session || parsed.token;
            break;
          }
        } catch (e) {
          if (value && value.length > 20) {
            data.sessionToken = value;
            break;
          }
        }
      }
    }

    // Search in cookies
    if (!data.sessionToken) {
      const cookieMatches = data.cookies.match(/session[^=]*=([^;]+)/i);
      if (cookieMatches) {
        data.sessionToken = cookieMatches[1];
      }
    }

    return data;
  });

  // Save session data
  const sessionFile = path.join(sessionsDir, `session_${Date.now()}.json`);
  await fs.writeFile(sessionFile, JSON.stringify(sessionData, null, 2));

  // Save cookies separately
  const cookiesFile = path.join(cookiesDir, `cookies_${Date.now()}.json`);
  await fs.writeFile(cookiesFile, JSON.stringify({
    timestamp: new Date().toISOString(),
    cookies: sessionData.cookies,
    domain: 'qxbroker.com'
  }, null, 2));

  monitoringState.sessionToken = sessionData.sessionToken;

  console.log('💾 تم حفظ بيانات الجلسة');
  if (sessionData.sessionToken) {
    console.log('🔑 Session Token:', sessionData.sessionToken.substring(0, 20) + '...');
  } else {
    console.log('⚠️ لم يتم العثور على Session Token');
  }
}

// Start message monitoring
async function startMessageMonitoring(page) {
  console.log('📡 بدء مراقبة الرسائل...');

  // Monitor messages every 5 seconds
  setInterval(async () => {
    try {
      const monitorData = await page.evaluate(() => {
        if (window.quotexMonitor) {
          return {
            messageCount: window.quotexMonitor.messageCount,
            isConnected: window.quotexMonitor.isConnected,
            recentMessages: window.quotexMonitor.messages.slice(-10),
            sessionData: window.quotexMonitor.sessionData
          };
        }
        return null;
      });

      if (monitorData) {
        monitoringState.totalMessages = monitorData.messageCount;
        monitoringState.isConnected = monitorData.isConnected;
        monitoringState.lastActivity = new Date();

        // Count message types
        monitorData.recentMessages.forEach(msg => {
          const type = msg.messageType || 'unknown';
          monitoringState.messageTypes[type] = (monitoringState.messageTypes[type] || 0) + 1;
        });

        // Save messages periodically
        if (monitorData.messageCount > 0 && monitorData.messageCount % 50 === 0) {
          const messagesFile = path.join(messagesDir, `messages_${Date.now()}.json`);
          await fs.writeFile(messagesFile, JSON.stringify(monitorData.recentMessages, null, 2));
          console.log(`💾 تم حفظ ${monitorData.messageCount} رسالة`);
        }

        // Log progress
        if (monitorData.messageCount % 10 === 0 && monitorData.messageCount > 0) {
          console.log(`📊 إجمالي الرسائل: ${monitorData.messageCount}`);
        }
      }
    } catch (error) {
      console.error('❌ خطأ في مراقبة الرسائل:', error.message);
      monitoringState.errors.push({
        timestamp: new Date().toISOString(),
        error: error.message
      });
    }
  }, 5000);
}

// Monitor for specified duration
async function monitorForDuration(page, duration) {
  console.log(`⏱️ بدء المراقبة لمدة ${duration / 1000} ثانية...`);

  const startTime = Date.now();
  const endTime = startTime + duration;

  while (Date.now() < endTime) {
    const remaining = Math.ceil((endTime - Date.now()) / 1000);
    if (remaining % 30 === 0) { // Log every 30 seconds
      console.log(`⏳ الوقت المتبقي: ${remaining} ثانية`);

      // Take periodic screenshots
      try {
        await page.screenshot({
          path: path.join(outputDir, `screenshot_${Date.now()}.png`),
          fullPage: false
        });
      } catch (e) {
        console.log('⚠️ فشل في أخذ لقطة شاشة');
      }
    }

    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  console.log('✅ انتهت فترة المراقبة');

  // Final data extraction
  await extractFinalData(page);
}

// Extract final comprehensive data
async function extractFinalData(page) {
  console.log('📊 استخراج البيانات النهائية...');

  try {
    const finalData = await page.evaluate(() => {
      if (window.quotexMonitor) {
        return {
          summary: {
            totalMessages: window.quotexMonitor.messageCount,
            monitoringDuration: Date.now() - window.quotexMonitor.startTime,
            isConnected: window.quotexMonitor.isConnected
          },
          allMessages: window.quotexMonitor.messages,
          sessionData: window.quotexMonitor.sessionData,
          messageTypes: {}
        };
      }
      return null;
    });

    if (finalData) {
      // Analyze message types
      finalData.allMessages.forEach(msg => {
        const type = msg.messageType || 'unknown';
        finalData.messageTypes[type] = (finalData.messageTypes[type] || 0) + 1;
      });

      // Save comprehensive report
      const reportFile = path.join(outputDir, `final_report_${Date.now()}.json`);
      await fs.writeFile(reportFile, JSON.stringify(finalData, null, 2));

      // Save all messages
      const allMessagesFile = path.join(messagesDir, `all_messages_${Date.now()}.json`);
      await fs.writeFile(allMessagesFile, JSON.stringify(finalData.allMessages, null, 2));

      console.log('📋 تقرير نهائي:');
      console.log(`   📨 إجمالي الرسائل: ${finalData.summary.totalMessages}`);
      console.log(`   ⏱️ مدة المراقبة: ${Math.round(finalData.summary.monitoringDuration / 1000)} ثانية`);
      console.log(`   🔗 حالة الاتصال: ${finalData.summary.isConnected ? 'متصل' : 'منقطع'}`);
      console.log('   📊 أنواع الرسائل:');
      Object.entries(finalData.messageTypes).forEach(([type, count]) => {
        console.log(`      ${type}: ${count}`);
      });
    }
  } catch (error) {
    console.error('❌ خطأ في استخراج البيانات النهائية:', error);
  }
}

// Web dashboard for real-time monitoring
function startWebDashboard() {
  const server = http.createServer((req, res) => {
    res.writeHead(200, {
      'Content-Type': 'text/html; charset=utf-8',
      'Access-Control-Allow-Origin': '*'
    });

    const html = `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مراقب Quotex - لوحة التحكم</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            direction: rtl;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid rgba(255,255,255,0.3);
            padding-bottom: 20px;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .status-card {
            background: rgba(255,255,255,0.2);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid rgba(255,255,255,0.3);
        }
        .status-value {
            font-size: 2em;
            font-weight: bold;
            margin: 10px 0;
        }
        .connected { color: #4CAF50; }
        .disconnected { color: #f44336; }
        .info-section {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .refresh-btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .refresh-btn:hover { background: #45a049; }
        .message-types {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .message-type {
            background: rgba(255,255,255,0.15);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 مراقب منصة Quotex</h1>
            <p>نظام المراقبة المتقدم للرسائل والبيانات</p>
            <button class="refresh-btn" onclick="location.reload()">🔄 تحديث</button>
        </div>

        <div class="status-grid">
            <div class="status-card">
                <h3>🔗 حالة الاتصال</h3>
                <div class="status-value ${monitoringState.isConnected ? 'connected' : 'disconnected'}">
                    ${monitoringState.isConnected ? '✅ متصل' : '❌ منقطع'}
                </div>
            </div>

            <div class="status-card">
                <h3>📨 إجمالي الرسائل</h3>
                <div class="status-value">${monitoringState.totalMessages}</div>
            </div>

            <div class="status-card">
                <h3>⏱️ وقت التشغيل</h3>
                <div class="status-value">
                    ${monitoringState.startTime ? Math.round((Date.now() - monitoringState.startTime) / 1000) + ' ثانية' : 'غير محدد'}
                </div>
            </div>

            <div class="status-card">
                <h3>🔑 Session Token</h3>
                <div class="status-value" style="font-size: 0.8em;">
                    ${monitoringState.sessionToken ? monitoringState.sessionToken.substring(0, 15) + '...' : 'غير موجود'}
                </div>
            </div>
        </div>

        <div class="info-section">
            <h3>📊 أنواع الرسائل</h3>
            <div class="message-types">
                ${Object.entries(monitoringState.messageTypes).map(([type, count]) =>
                    `<div class="message-type">
                        <strong>${type}</strong><br>
                        <span style="font-size: 1.5em;">${count}</span>
                    </div>`
                ).join('')}
            </div>
        </div>

        <div class="info-section">
            <h3>📋 معلومات النظام</h3>
            <p><strong>آخر نشاط:</strong> ${monitoringState.lastActivity ? monitoringState.lastActivity.toLocaleString('ar-SA') : 'لا يوجد'}</p>
            <p><strong>عدد الأخطاء:</strong> ${monitoringState.errors.length}</p>
            <p><strong>الملفات المحفوظة:</strong> sessions/, cookies/, messages/</p>
        </div>

        <div class="info-section">
            <h3>📝 تعليمات الاستخدام</h3>
            <ul>
                <li>اترك النظام يعمل لمدة 5-10 دقائق على الأقل</li>
                <li>تأكد من أن المنصة محملة بالكامل</li>
                <li>قم بالتفاعل مع المنصة (تغيير الأدوات، الأسعار، إلخ)</li>
                <li>راقب عدد الرسائل - يجب أن يزيد باستمرار</li>
                <li>تحقق من وجود Session Token</li>
            </ul>
        </div>
    </div>

    <script>
        // Auto refresh every 5 seconds
        setTimeout(() => location.reload(), 5000);
    </script>
</body>
</html>`;

    res.end(html);
  });

  server.listen(3000, () => {
    console.log('🌐 لوحة التحكم متاحة على: http://localhost:3000');
  });
}
    
    // Analyze the chart area
    console.log('Analyzing chart components...');
    const chartSelectors = await page.evaluate(() => {
      // Look for chart elements based on common patterns in trading platforms
      const selectors = {};
      
      // Check for canvas elements (likely used for charts)
      const canvases = Array.from(document.querySelectorAll('canvas'));
      selectors.chartCanvases = canvases.map(canvas => {
        const rect = canvas.getBoundingClientRect();
        return {
          id: canvas.id,
          width: canvas.width,
          height: canvas.height,
          position: {
            top: rect.top,
            left: rect.left,
            width: rect.width,
            height: rect.height
          }
        };
      });
      
      // Find timeframe selector elements
      const timeframeSelectors = Array.from(document.querySelectorAll('select, button')).filter(el => {
        const text = el.textContent.toLowerCase();
        return text.includes('minute') || text.includes('hour') || text.includes('day') || 
               text.includes('1m') || text.includes('5m') || text.includes('15m') || 
               text.includes('30m') || text.includes('1h');
      });
      
      selectors.timeframeElements = timeframeSelectors.map(el => {
        return {
          tagName: el.tagName,
          id: el.id,
          class: el.className,
          text: el.textContent.trim()
        };
      });
      
      // Find indicator controls
      const indicatorElements = Array.from(document.querySelectorAll('button, div')).filter(el => {
        const text = el.textContent.toLowerCase();
        return text.includes('indicator') || text.includes('bollinger') || 
               text.includes('macd') || text.includes('rsi') || text.includes('sma') || 
               text.includes('ema');
      });
      
      selectors.indicatorElements = indicatorElements.map(el => {
        return {
          tagName: el.tagName,
          id: el.id,
          class: el.className,
          text: el.textContent.trim()
        };
      });
      
      // Extract colors used in the platform
      const computedStyles = window.getComputedStyle(document.body);
      const backgroundColor = computedStyles.backgroundColor;
      
      // Find all elements with explicit background-color
      const colorElements = Array.from(document.querySelectorAll('*')).filter(el => {
        const style = window.getComputedStyle(el);
        return style.backgroundColor && style.backgroundColor !== 'rgba(0, 0, 0, 0)';
      });
      
      const colorMap = {};
      colorElements.forEach(el => {
        const style = window.getComputedStyle(el);
        const bgColor = style.backgroundColor;
        if (!colorMap[bgColor]) {
          colorMap[bgColor] = 0;
        }
        colorMap[bgColor]++;
      });
      
      selectors.colors = {
        backgroundColor,
        colorMap: Object.entries(colorMap)
          .sort((a, b) => b[1] - a[1])
          .slice(0, 10) // Get top 10 most used colors
      };
      
      return selectors;
    });
    
    // Analyze the trading interface components
    console.log('Analyzing trading interface components...');
    const tradingInterface = await page.evaluate(() => {
      const components = {};
      
      // Find asset selector
      components.assetSelector = Array.from(document.querySelectorAll('select, button, div')).filter(el => {
        const text = el.textContent.toLowerCase();
        return text.includes('asset') || text.includes('symbol') || text.includes('eur/usd') || 
               text.includes('btc') || text.includes('currency');
      }).map(el => ({
        tagName: el.tagName,
        id: el.id,
        class: el.className,
        text: el.textContent.trim()
      }));
      
      // Find trade amount input
      components.amountInput = Array.from(document.querySelectorAll('input, button, div')).filter(el => {
        const text = el.textContent.toLowerCase();
        const placeholder = el.placeholder ? el.placeholder.toLowerCase() : '';
        return text.includes('amount') || text.includes('investment') || 
               placeholder.includes('amount') || placeholder.includes('investment');
      }).map(el => ({
        tagName: el.tagName,
        id: el.id,
        class: el.className,
        text: el.textContent.trim(),
        placeholder: el.placeholder
      }));
      
      // Find buy/sell buttons
      components.actionButtons = Array.from(document.querySelectorAll('button, div')).filter(el => {
        const text = el.textContent.toLowerCase();
        return text.includes('buy') || text.includes('sell') || 
               text.includes('up') || text.includes('down') || 
               text.includes('call') || text.includes('put');
      }).map(el => ({
        tagName: el.tagName,
        id: el.id,
        class: el.className,
        text: el.textContent.trim(),
        bgColor: window.getComputedStyle(el).backgroundColor,
        color: window.getComputedStyle(el).color
      }));
      
      // Find expiration time selector
      components.expirationSelector = Array.from(document.querySelectorAll('select, button, div')).filter(el => {
        const text = el.textContent.toLowerCase();
        return text.includes('expiration') || text.includes('expiry') || 
               text.includes('duration') || text.includes('time');
      }).map(el => ({
        tagName: el.tagName,
        id: el.id,
        class: el.className,
        text: el.textContent.trim()
      }));
      
      return components;
    });
    
    // Find WebSocket connections if any
    const wsConnections = await page.evaluate(() => {
      return Object.keys(window).filter(key => key.includes('socket') || key.includes('ws')); 
    });
    
    // Save analysis results to files
    fs.writeFileSync(
      path.join(outputDir, 'chart_components.json'), 
      JSON.stringify(chartSelectors, null, 2)
    );
    
    fs.writeFileSync(
      path.join(outputDir, 'trading_interface.json'), 
      JSON.stringify(tradingInterface, null, 2)
    );
    
    fs.writeFileSync(
      path.join(outputDir, 'api_requests.json'), 
      JSON.stringify(apiRequests, null, 2)
    );
    
    fs.writeFileSync(
      path.join(outputDir, 'websocket_connections.json'), 
      JSON.stringify(wsConnections, null, 2)
    );
    
    // Take screenshots of specific components if they were found
    if (chartSelectors.chartCanvases.length > 0) {
      const chartCanvas = chartSelectors.chartCanvases[0];
      await page.screenshot({
        path: path.join(outputDir, 'chart_component.png'),
        clip: {
          x: chartCanvas.position.left,
          y: chartCanvas.position.top,
          width: chartCanvas.position.width,
          height: chartCanvas.position.height
        }
      });
    }
    
    if (tradingInterface.actionButtons.length > 0) {
      // Find relevant elements by class or tag if IDs aren't available
      for (const button of tradingInterface.actionButtons) {
        let selector;
        if (button.id) {
          selector = `#${button.id}`;
        } else if (button.class) {
          selector = `.${button.class.split(' ').join('.')}`;
        } else {
          continue;
        }
        
        try {
          const el = await page.$(selector);
          if (el) {
            await el.screenshot({
              path: path.join(outputDir, `action_button_${button.text.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.png`)
            });
          }
        } catch (e) {
          console.log(`Couldn't screenshot button with selector ${selector}`);
        }
      }
    }
    
    console.log('Analysis complete. Results saved to the analysis_results directory.');
    
  } catch (error) {
    console.error('Error during analysis:', error);
    // Save error information
    fs.writeFileSync(
      path.join(outputDir, 'error.txt'),
      `Error during analysis: ${error.message}\n${error.stack}`
    );
  } finally {
    // Wait a bit before closing to see the page
    await page.waitForTimeout(10000);
    await browser.close();
  }
}

  } catch (error) {
    console.error('❌ خطأ في النظام:', error);
    monitoringState.errors.push({
      timestamp: new Date().toISOString(),
      error: error.message,
      stack: error.stack
    });
  } finally {
    console.log('🔚 إنهاء المراقبة...');

    // Generate final summary
    console.log('\n📋 ملخص نهائي:');
    console.log(`📨 إجمالي الرسائل: ${monitoringState.totalMessages}`);
    console.log(`🔗 حالة الاتصال: ${monitoringState.isConnected ? 'متصل' : 'منقطع'}`);
    console.log(`🔑 Session Token: ${monitoringState.sessionToken ? 'موجود' : 'غير موجود'}`);
    console.log(`⚠️ عدد الأخطاء: ${monitoringState.errors.length}`);
    console.log(`📁 الملفات محفوظة في: ${outputDir}`);

    if (monitoringState.totalMessages === 0) {
      console.log('\n⚠️ تحذير: لم يتم التقاط أي رسائل!');
      console.log('💡 اقتراحات:');
      console.log('   1. تأكد من تحميل المنصة بالكامل');
      console.log('   2. تفاعل مع المنصة (غير الأدوات، اضغط على الأزرار)');
      console.log('   3. انتظر وقتاً أطول');
      console.log('   4. تحقق من اتصال الإنترنت');
    } else if (monitoringState.totalMessages < 10) {
      console.log('\n⚠️ تحذير: عدد قليل من الرسائل!');
      console.log('💡 للحصول على بيانات أفضل:');
      console.log('   1. اترك النظام يعمل لفترة أطول');
      console.log('   2. تفاعل أكثر مع المنصة');
    } else {
      console.log('\n✅ تم جمع البيانات بنجاح!');
      console.log('📊 يمكنك الآن تحليل الملفات المحفوظة');
    }

    // Keep browser open for manual inspection
    console.log('\n🔍 المتصفح سيبقى مفتوحاً لمدة دقيقة للفحص اليدوي...');
    await new Promise(resolve => setTimeout(resolve, 60000));

    await browser.close();
  }
}

// Start the enhanced monitoring system
startQuotexMonitoring().catch(console.error);